apiVersion: apps/v1
kind: Deployment
metadata:
  name: bcmonitoring-java
  namespace: dcbg-core
  labels:
    app: bcmonitoring-java
spec:
  selector:
    matchLabels:
      app: bcmonitoring-java
  template:
    metadata:
      name: "bcmonitoring-java_pod"
      labels:
        app: bcmonitoring-java
      annotations:
        checksum/configmap: {{ include (print $.Template.BasePath "/configmap-bcmonitoring-java-envs.yaml") . | sha256sum }}
    spec:
      shareProcessNamespace: true
      serviceAccountName: {{ .Values.serviceAccount.name }}
      containers:
        - name: bcmonitoring-java
          image: {{ .Values.application.image.name }}:{{ .Values.application.image.tag }}
          imagePullPolicy: {{ .Values.application.image.pullPolicy }}
          ports:
            - containerPort: {{ .Values.application.port }}
              protocol: TCP
          envFrom:
            - configMapRef:
                name: bcmonitoring-java-envs
          resources:
            requests:
              cpu: {{ .Values.application.resources.requests.cpu }}
              memory: {{ .Values.application.resources.requests.memory }}
            limits:
              cpu: {{ .Values.application.resources.limits.cpu }}
              memory: {{ .Values.application.resources.limits.memory }}
          livenessProbe:
            httpGet:
              path: /actuator/health
              port: {{ .Values.application.port }}
            initialDelaySeconds: 60
            periodSeconds: 30
            timeoutSeconds: 10
          readinessProbe:
            httpGet:
              path: /actuator/health/readiness
              port: {{ .Values.application.port }}
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
        - name: newrelic-infrastructure
          image: {{ .Values.newrelic.imageName }}:{{ .Values.newrelic.imageTag }}
          imagePullPolicy: IfNotPresent
          env:
            - name: NRIA_LICENSE_KEY
              valueFrom:
                secretKeyRef:
                  key: license-key
                  name: newrelic-license-key
            - name: NRIA_LOG_LEVEL
              value: "info"
            - name: NRIA_VERBOSE
              value: "0"
            - name: DISABLE_KUBE_STATE_METRICS
              value: "true"
            - name: CLUSTER_NAME
              value: {{ .Values.envName }}-eks-cluster
            - name: COMPUTE_TYPE
              value: serverless
            - name: NRK8S_NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_DISPLAY_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NRIA_CUSTOM_ATTRIBUTES
              value: '{"clusterName":"$(CLUSTER_NAME)", "Environment":"{{ .Values.envName }}"}'
            - name: NRIA_PASSTHROUGH_ENVIRONMENT
              value: KUBERNETES_SERVICE_HOST,KUBERNETES_SERVICE_PORT,CLUSTER_NAME,CADVISOR_PORT,NRK8S_NODE_NAME,KUBE_STATE_METRICS_URL,KUBE_STATE_METRICS_POD_LABEL,TIMEOUT,ETCD_TLS_SECRET_NAME,ETCD_TLS_SECRET_NAMESPACE,API_SERVER_SECURE_PORT,KUBE_STATE_METRICS_SCHEME,KUBE_STATE_METRICS_PORT,SCHEDULER_ENDPOINT_URL,ETCD_ENDPOINT_URL,CONTROLLER_MANAGER_ENDPOINT_URL,API_SERVER_ENDPOINT_URL,DISABLE_KUBE_STATE_METRICS,DISCOVERY_CACHE_TTL
          resources:
            limits:
              cpu: {{ .Values.newrelic.resources.limits.cpu }}
              memory: {{ .Values.newrelic.resources.limits.memory }}
            requests:
              cpu: {{ .Values.newrelic.resources.requests.cpu }}
              memory: {{ .Values.newrelic.resources.requests.memory }}
