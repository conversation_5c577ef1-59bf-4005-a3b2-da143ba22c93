# 担当者別タスクリスト - BCMonitoring Java Kubernetesデプロイ

## プロジェクト概要

**目標**: `dcbg-dcjpy-bcmonitoring-java`アプリケーションをKubernetes経由でデプロイする

**対象環境**: dev-fin-tokyo, dev-biz-tokyo, stage, prod

---

## 🔧 DLT開発者担当タスク

### 優先度：高（必須）

#### 1. Java版BCMonitoringのイメージをビルドしてECRにプッシュするGithub Action作成
- **内容**: 既存のJavaアプリケーションをDockerイメージとしてビルドし、各環境のECRにプッシュするCI/CDパイプラインを作成
- **目的**: 自動化されたイメージビルドとデプロイメント
- **成果物**: `.github/workflows/build-and-push.yml`ファイル

---

## ☁️ Cloud開発者担当タスク

### 優先度：高（必須）

#### 1. ECRリポジトリ作成 - 全環境対応
- **内容**: 新しいJavaアプリケーション用のECRリポジトリを各環境に作成
- **対象環境**: dev-fin, dev-biz, stage, prod
- **成果物**: ECRリポジトリURL、アクセス権限設定

#### 2. IAMロール・ServiceAccount作成 - AWS権限設定
- **内容**: Kubernetes ServiceAccount用のIAMロールを各環境に作成
- **権限**: S3、DynamoDB等へのアクセス権限
- **成果物**: IAMロールARN、ServiceAccount設定

#### 3. 環境別values.yaml完成 - 具体的設定値調査
- **内容**: 各環境の具体的な設定値を調査し、values.yamlファイルを完成
- **設定項目**: WebSocket URI、S3バケット名、IAMロールARN等
- **成果物**: 完成したvalues.yamlファイル（全環境分）

### 優先度：中（推奨）

#### 4. New Relic設定 - モニタリング準備
- **内容**: 各環境のKubernetesクラスターにNew RelicライセンスキーのSecretを作成
- **成果物**: モニタリング設定の完了

### 優先度：低（運用）

#### 5. ネットワーク・セキュリティ設定 - 運用準備
- **内容**: Kubernetesネットワークポリシー、セキュリティグループ、ロードバランサー設定
- **成果物**: セキュリティ設定の完了

#### 6. モニタリング・アラート設定 - 監視体制
- **内容**: アプリケーションのメトリクス監視、ログ監視、アラート設定を構築
- **成果物**: 運用監視体制の整備

---

## 📋 作業順序の推奨

### フェーズ1: 基盤準備（並行作業可能）
- **BE開発者**: Spring Boot Actuator実装、設定外部化
- **Cloud開発者**: ECRリポジトリ作成、IAMロール作成

### フェーズ2: 統合準備
- **BE開発者**: ログ設定最適化、アプリケーションビルド
- **Cloud開発者**: 環境別values.yaml完成、New Relic設定

### フェーズ3: デプロイとテスト
- **共同作業**: dev環境でのテストデプロイ
- **Cloud開発者**: CI/CDパイプライン構築、本番デプロイ

### フェーズ4: 運用準備
- **Cloud開発者**: モニタリング設定、セキュリティ設定

---

## 🎯 成果物一覧

### 作成済み成果物
- ✅ Helmチャート一式（Chart.yaml, values.yaml, templates/）
- ✅ 環境別設定ファイル（dev-fin, dev-biz用values.yaml）
- ✅ デプロイ手順書（dcbg-dcjpy-bcmonitoring-java-deployment-guide.md）

### 今後作成予定の成果物
- 🔄 完成したJavaアプリケーション（Actuator対応）
- 🔄 全環境のvalues.yamlファイル
- 🔄 CI/CDパイプライン設定
- 🔄 運用監視設定

---

## 📞 連携ポイント

1. **設定値の確認**: BE開発者とCloud開発者間で環境変数の仕様を確認
2. **テストデプロイ**: dev環境での動作確認を共同で実施
3. **トラブルシューティング**: デプロイ時の問題解決を協力して対応

## 📅 推奨スケジュール

- **Week 1**: フェーズ1（基盤準備）
- **Week 2**: フェーズ2（統合準備）
- **Week 3**: フェーズ3（デプロイとテスト）
- **Week 4**: フェーズ4（運用準備）
