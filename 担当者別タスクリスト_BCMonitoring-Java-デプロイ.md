# 担当者別タスクリスト - BCMonitoring Java Kubernetesデプロイ

## プロジェクト概要

**目標**: `dcbg-dcjpy-bcmonitoring-java`アプリケーションをKubernetes経由でデプロイする

**対象環境**: dev-fin-tokyo, dev-biz-tokyo, stage, prod

---

## 🔧 BE（バックエンド）開発者担当タスク

### 優先度：高（必須）

#### 1. Spring Boot Actuatorエンドポイントの実装
- **内容**: ヘルスチェック用のActuatorエンドポイント（/actuator/health, /actuator/health/readiness）を実装
- **目的**: Kubernetesのliveness/readinessプローブに対応
- **成果物**: application.ymlの設定、必要な依存関係の追加

#### 2. アプリケーション設定の外部化
- **内容**: 環境変数による設定管理を実装し、ConfigMapから読み込む環境変数に対応
- **目的**: 環境別設定の柔軟な管理
- **成果物**: application.ymlの環境変数対応設定

#### 3. ログ設定の最適化
- **内容**: Kubernetes環境に適したログ出力設定（JSON形式、適切なログレベル）を実装
- **目的**: コンテナログとして適切に出力される設定
- **成果物**: logback-spring.xmlの設定

### 優先度：中（推奨）

#### 4. Graceful Shutdownの実装
- **内容**: Kubernetesのpod終了シグナル（SIGTERM）に対応したGraceful Shutdownを実装
- **目的**: 処理中のタスクを適切に終了させる
- **成果物**: シャットダウンフック、設定の追加

#### 5. Docker Entrypointスクリプトの最適化
- **内容**: Kubernetes環境に適したdocker-entrypoint.shの設定
- **目的**: JVMオプションやシグナルハンドリングの最適化
- **成果物**: 最適化されたdocker-entrypoint.sh

### 優先度：低（基本）

#### 6. アプリケーションのビルドとテスト
- **内容**: Gradleビルドの実行、単体テストの実行、JARファイルの生成
- **目的**: Dockerイメージ作成の準備
- **成果物**: ビルド済みJARファイル

---

## ☁️ Cloud開発者担当タスク

### 優先度：高（必須）

#### 1. ECRリポジトリの作成
- **内容**: 新しいJavaアプリケーション用のECRリポジトリを各環境に作成
- **対象環境**: dev-fin, dev-biz, stage, prod
- **成果物**: ECRリポジトリURL、アクセス権限設定

#### 2. IAMロールとサービスアカウントの作成
- **内容**: Kubernetes ServiceAccount用のIAMロールを各環境に作成
- **権限**: S3、DynamoDB等へのアクセス権限
- **成果物**: IAMロールARN、ServiceAccount設定

#### 3. 環境別values.yamlファイルの完成
- **内容**: 各環境の具体的な設定値を調査し、values.yamlファイルを完成
- **設定項目**: WebSocket URI、S3バケット名、IAMロールARN等
- **成果物**: 完成したvalues.yamlファイル（全環境分）

### 優先度：中（推奨）

#### 4. CI/CDパイプラインの構築
- **内容**: Dockerイメージのビルド、ECRへのプッシュ、Helmデプロイメントを含むパイプライン
- **成果物**: CI/CDパイプライン設定、自動デプロイ機能

#### 5. New Relicライセンスキーの設定
- **内容**: 各環境のKubernetesクラスターにNew RelicライセンスキーのSecretを作成
- **成果物**: モニタリング設定の完了

#### 6. Helmチャートのテストとデプロイ
- **内容**: 作成したHelmチャートをdev環境でテストし、stage/prod環境にデプロイ
- **成果物**: 動作確認済みのデプロイメント

### 優先度：低（運用）

#### 7. ネットワークとセキュリティ設定
- **内容**: Kubernetesネットワークポリシー、セキュリティグループ、ロードバランサー設定
- **成果物**: セキュリティ設定の完了

#### 8. モニタリングとアラート設定
- **内容**: アプリケーションのメトリクス監視、ログ監視、アラート設定を構築
- **成果物**: 運用監視体制の整備

---

## 📋 作業順序の推奨

### フェーズ1: 基盤準備（並行作業可能）
- **BE開発者**: Spring Boot Actuator実装、設定外部化
- **Cloud開発者**: ECRリポジトリ作成、IAMロール作成

### フェーズ2: 統合準備
- **BE開発者**: ログ設定最適化、アプリケーションビルド
- **Cloud開発者**: 環境別values.yaml完成、New Relic設定

### フェーズ3: デプロイとテスト
- **共同作業**: dev環境でのテストデプロイ
- **Cloud開発者**: CI/CDパイプライン構築、本番デプロイ

### フェーズ4: 運用準備
- **Cloud開発者**: モニタリング設定、セキュリティ設定

---

## 🎯 成果物一覧

### 作成済み成果物
- ✅ Helmチャート一式（Chart.yaml, values.yaml, templates/）
- ✅ 環境別設定ファイル（dev-fin, dev-biz用values.yaml）
- ✅ デプロイ手順書（dcbg-dcjpy-bcmonitoring-java-deployment-guide.md）

### 今後作成予定の成果物
- 🔄 完成したJavaアプリケーション（Actuator対応）
- 🔄 全環境のvalues.yamlファイル
- 🔄 CI/CDパイプライン設定
- 🔄 運用監視設定

---

## 📞 連携ポイント

1. **設定値の確認**: BE開発者とCloud開発者間で環境変数の仕様を確認
2. **テストデプロイ**: dev環境での動作確認を共同で実施
3. **トラブルシューティング**: デプロイ時の問題解決を協力して対応

## 📅 推奨スケジュール

- **Week 1**: フェーズ1（基盤準備）
- **Week 2**: フェーズ2（統合準備）
- **Week 3**: フェーズ3（デプロイとテスト）
- **Week 4**: フェーズ4（運用準備）
